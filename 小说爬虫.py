import requests
from bs4 import BeautifulSoup
import time
import random
import re
import logging
from datetime import datetime
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('小说爬虫日志.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_novel_content(url, max_retries=3):
    """
    获取小说网页内容（带重试机制）

    Args:
        url (str): 目标网址
        max_retries (int): 最大重试次数

    Returns:
        str: 网页内容
    """
    # 设置请求头，模拟浏览器访问
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    for attempt in range(max_retries):
        try:
            # 发送GET请求
            if attempt == 0:
                print(f"📡 正在请求: {url}")
            else:
                print(f"🔄 重试第 {attempt} 次: {url}")

            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()  # 检查请求是否成功

            # 设置编码
            response.encoding = 'utf-8'

            print(f"✅ 请求成功，状态码: {response.status_code}")
            logger.info(f"成功获取网页内容: {url}")

            return response.text

        except requests.exceptions.RequestException as e:
            error_msg = f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}"
            print(f"❌ {error_msg}")
            logger.warning(f"网络请求失败: {url} - {e}")

            if attempt < max_retries - 1:
                # 递增延迟：1秒、2秒、3秒
                delay = attempt + 1
                print(f"⏳ {delay} 秒后重试...")
                time.sleep(delay)
            else:
                logger.error(f"所有重试均失败: {url}")
                return None

    return None

def extract_chapter_content(html_content):
    """
    提取章节内容

    Args:
        html_content (str): HTML内容

    Returns:
        dict: 包含章节标题和内容的字典
    """
    if not html_content:
        logger.warning("HTML内容为空，无法提取章节内容")
        return None

    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        result = {
            'title': '',
            'content': '',
            'next_page_url': None
        }

        # 提取章节标题
        title_element = soup.find('h1', class_='headline')
        if title_element:
            result['title'] = title_element.get_text().strip()
            logger.debug(f"提取到章节标题: {result['title']}")

        # 提取章节内容
        content_element = soup.find('div', class_='content')
        if content_element:
            # 获取所有段落
            paragraphs = content_element.find_all('p')
            content_lines = []

            for p in paragraphs:
                text = p.get_text().strip()
                if text and text not in ['', '\n', '\r\n']:  # 过滤空内容
                    content_lines.append(text)

            result['content'] = '\n\n'.join(content_lines)
            logger.debug(f"提取到内容段落数: {len(content_lines)}")

        # 检查是否有下一页
        pager_element = soup.find('div', class_='pager')
        if pager_element:
            next_link = pager_element.find('a', string='下一页')
            if next_link:
                next_url = next_link.get('href')
                if next_url.startswith('/'):
                    next_url = 'https://m.shuhaige.net' + next_url
                result['next_page_url'] = next_url
                logger.debug(f"找到下一页链接: {next_url}")

        return result

    except Exception as e:
        logger.error(f"解析章节内容时出错: {e}")
        return None

def get_full_chapter_content(chapter_url):
    """
    获取完整章节内容（包括分页）

    Args:
        chapter_url (str): 章节URL

    Returns:
        dict: 包含完整章节内容的字典
    """
    full_content = []
    current_url = chapter_url
    page_num = 1
    chapter_data = None

    try:
        while current_url:
            print(f"📖 正在获取第 {page_num} 页内容...")

            html_content = get_novel_content(current_url)
            if not html_content:
                print(f"❌ 获取第 {page_num} 页失败")
                logger.error(f"获取章节第 {page_num} 页失败: {current_url}")
                break

            chapter_data = extract_chapter_content(html_content)
            if not chapter_data:
                print(f"❌ 解析第 {page_num} 页失败")
                logger.error(f"解析章节第 {page_num} 页失败: {current_url}")
                break

            # 第一页包含标题
            if page_num == 1:
                full_content.append(f"# {chapter_data['title']}")
                full_content.append("")  # 空行
                print(f"📝 章节标题: {chapter_data['title']}")

            # 添加内容
            if chapter_data['content']:
                full_content.append(chapter_data['content'])
                full_content.append("")  # 空行
                print(f"✅ 第 {page_num} 页内容已获取")

            # 检查是否有下一页
            if chapter_data['next_page_url']:
                current_url = chapter_data['next_page_url']
                page_num += 1
                # 添加延迟
                time.sleep(random.uniform(0.5, 1.5))
            else:
                current_url = None

        result = {
            'title': chapter_data['title'] if chapter_data else '',
            'content': '\n'.join(full_content)
        }

        if page_num > 1:
            print(f"📚 完整章节获取完成，共 {page_num} 页")

        return result

    except Exception as e:
        logger.error(f"获取完整章节内容时出错: {e}")
        return {
            'title': chapter_data['title'] if chapter_data else '',
            'content': '\n'.join(full_content) if full_content else ''
        }

def create_formatted_filename(novel_title):
    """
    创建格式化的文件名

    Args:
        novel_title (str): 小说标题

    Returns:
        str: 格式化的文件名
    """
    # 移除不合法的文件名字符
    invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    clean_title = novel_title
    for char in invalid_chars:
        clean_title = clean_title.replace(char, '_')

    # 限制文件名长度
    if len(clean_title) > 50:
        clean_title = clean_title[:50]

    return f"{clean_title}.txt"

def save_novel_to_txt(chapters_data, novel_title='未知小说'):
    """
    保存小说到txt文件（优化格式）

    Args:
        chapters_data (list): 章节数据列表
        novel_title (str): 小说标题
    """
    try:
        filename = create_formatted_filename(novel_title)

        with open(filename, 'w', encoding='utf-8') as f:
            # 写入小说标题和信息
            f.write(f"《{novel_title}》\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总章节数: {len(chapters_data)}\n")
            f.write("=" * 60 + "\n\n")

            # 写入所有章节
            for i, chapter in enumerate(chapters_data, 1):
                print(f"💾 正在保存第 {i} 章: {chapter['title']}")

                # 章节分隔符
                f.write(f"\n{'=' * 60}\n")
                f.write(f"第 {i} 章\n")
                f.write(f"{'=' * 60}\n\n")

                # 章节内容
                f.write(chapter['content'])
                f.write("\n\n")

        print(f"📚 小说已保存到文件: {filename}")
        logger.info(f"小说保存成功: {filename}, 共 {len(chapters_data)} 章")
        return filename

    except Exception as e:
        error_msg = f"保存文件失败: {e}"
        print(f"❌ {error_msg}")
        logger.error(error_msg)
        return None

def append_chapter_to_txt(chapter_data, filename, is_first=False, chapter_num=1):
    """
    实时追加章节到txt文件（优化格式）

    Args:
        chapter_data (dict): 章节数据
        filename (str): 文件名
        is_first (bool): 是否是第一章节
        chapter_num (int): 章节编号
    """
    try:
        mode = 'w' if is_first else 'a'
        with open(filename, mode, encoding='utf-8') as f:
            if is_first:
                # 写入小说标题和信息
                novel_title = chapter_data.get('novel_title', '未知小说')
                f.write(f"《{novel_title}》\n")
                f.write("=" * 60 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 60 + "\n\n")

            # 章节分隔符
            f.write(f"\n{'=' * 60}\n")
            f.write(f"第 {chapter_num} 章\n")
            f.write(f"{'=' * 60}\n\n")

            # 写入章节内容
            f.write(chapter_data['content'])
            f.write("\n\n")

        print(f"✅ 第 {chapter_num} 章已保存: {chapter_data['title']}")
        logger.info(f"章节保存成功: 第 {chapter_num} 章 - {chapter_data['title']}")

    except Exception as e:
        error_msg = f"保存第 {chapter_num} 章失败: {e}"
        print(f"❌ {error_msg}")
        logger.error(error_msg)

def extract_chapters_list(html_content):
    """
    提取章节列表信息

    Args:
        html_content (str): HTML内容

    Returns:
        dict: 包含章节信息的字典
    """
    if not html_content:
        logger.warning("HTML内容为空，无法提取章节列表")
        return None

    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        result = {
            'novel_title': '',
            'total_chapters': 0,
            'chapters': [],
            'navigation': {}
        }

        # 提取小说标题
        title_element = soup.find('div', class_='read')
        if title_element:
            title_text = title_element.find_previous_sibling('div')
            if title_text:
                title_text = title_text.get_text().strip()
                if '【' in title_text and '】' in title_text:
                    start = title_text.find('【') + 1
                    end = title_text.find('】')
                    if start > 0 and end > start:
                        result['novel_title'] = title_text[start:end]

        # 提取总章节数
        if title_element:
            title_text = title_element.find_previous_sibling('div')
            if title_text:
                text = title_text.get_text()
                if '共' in text and '章' in text:
                    try:
                        numbers = re.findall(r'\d+', text)
                        if numbers:
                            result['total_chapters'] = int(numbers[-1])
                    except:
                        pass

        # 提取章节列表
        chapter_list = soup.find('ul', class_='read')
        if chapter_list:
            chapter_items = chapter_list.find_all('li')
            for item in chapter_items:
                chapter_link = item.find('a')
                if chapter_link:
                    chapter_id = item.get('chapter-id', '')
                    chapter_title = chapter_link.get_text().strip()
                    chapter_url = chapter_link.get('href', '')

                    if chapter_url.startswith('/'):
                        chapter_url = 'https://m.shuhaige.net' + chapter_url

                    result['chapters'].append({
                        'id': chapter_id,
                        'title': chapter_title,
                        'url': chapter_url
                    })

        # 提取导航信息
        nav_links = soup.find_all('a')
        for link in nav_links:
            href = link.get('href', '')
            text = link.get_text().strip()

            if '下一页' in text:
                if href.startswith('/'):
                    href = 'https://m.shuhaige.net' + href
                result['navigation']['next_page'] = href
            elif '尾页' in text:
                if href.startswith('/'):
                    href = 'https://m.shuhaige.net' + href
                result['navigation']['last_page'] = href
            elif '首页' in text:
                if href.startswith('/'):
                    href = 'https://m.shuhaige.net' + href
                result['navigation']['first_page'] = href

        logger.info(f"提取章节列表成功: {result['novel_title']}, 当前页 {len(result['chapters'])} 章")
        return result

    except Exception as e:
        logger.error(f"解析章节列表时出错: {e}")
        return None

def get_all_chapters_list(base_url):
    """
    获取所有章节列表（包括分页）

    Args:
        base_url (str): 基础URL

    Returns:
        dict: 包含所有章节信息的字典
    """
    all_chapters = []
    current_url = base_url
    page_count = 0
    novel_title = ''
    total_chapters = 0

    print("🔍 开始获取章节列表...")

    try:
        while current_url:
            page_count += 1
            print(f"📄 正在获取第 {page_count} 页章节列表...")

            html_content = get_novel_content(current_url)
            if not html_content:
                print(f"❌ 获取第 {page_count} 页失败，停止获取")
                break

            chapter_data = extract_chapters_list(html_content)
            if chapter_data and chapter_data['chapters']:
                all_chapters.extend(chapter_data['chapters'])

                # 保存小说标题和总章节数
                if page_count == 1:
                    novel_title = chapter_data['novel_title']
                    total_chapters = chapter_data['total_chapters']

                print(f"✅ 第 {page_count} 页找到 {len(chapter_data['chapters'])} 个章节")
            else:
                print(f"⚠️ 第 {page_count} 页未找到章节")

            # 获取下一页链接
            soup = BeautifulSoup(html_content, 'html.parser')
            next_link = soup.find('a', text='下一页')
            if next_link:
                next_url = next_link.get('href')
                if next_url.startswith('/'):
                    next_url = 'https://m.shuhaige.net' + next_url
                current_url = next_url
                print(f"🔗 找到下一页链接")
            else:
                print("✅ 所有章节列表获取完成")
                current_url = None

            # 添加延迟
            time.sleep(random.uniform(1, 2))

        result = {
            'novel_title': novel_title,
            'total_chapters': len(all_chapters),
            'expected_chapters': total_chapters,
            'chapters': all_chapters
        }

        print(f"📚 章节列表获取完成:")
        print(f"   小说标题: {novel_title}")
        print(f"   获取章节: {len(all_chapters)} 章")
        print(f"   预期章节: {total_chapters} 章")

        logger.info(f"章节列表获取完成: {novel_title}, 获取 {len(all_chapters)} 章")
        return result

    except Exception as e:
        logger.error(f"获取章节列表时出错: {e}")
        return None

def crawl_all_chapters(chapter_urls, novel_title='未知小说'):
    """
    爬取所有章节内容并实时保存

    Args:
        chapter_urls (list): 章节URL列表
        novel_title (str): 小说标题

    Returns:
        list: 章节内容列表
    """
    all_chapters = []
    total_chapters = len(chapter_urls)
    filename = create_formatted_filename(novel_title)

    print(f"🚀 开始爬取小说: 《{novel_title}》")
    print(f"📊 总章节数: {total_chapters}")
    print(f"💾 保存文件: {filename}")
    print("=" * 60)

    start_time = datetime.now()

    for i, chapter_info in enumerate(chapter_urls, 1):
        chapter_title = chapter_info['title']
        chapter_url = chapter_info['url']

        print(f"\n📖 进度: {i}/{total_chapters} ({i/total_chapters*100:.1f}%)")
        print(f"📝 章节: {chapter_title}")

        try:
            # 获取章节内容
            chapter_content = get_full_chapter_content(chapter_url)

            if chapter_content and chapter_content['content']:
                chapter_data = {
                    'title': chapter_content['title'],
                    'content': chapter_content['content'],
                    'novel_title': novel_title
                }

                # 实时保存到文件
                is_first = (i == 1)
                append_chapter_to_txt(chapter_data, filename, is_first, i)

                all_chapters.append(chapter_data)
                print(f"✅ 第 {i} 章爬取成功")
            else:
                print(f"❌ 第 {i} 章爬取失败")
                logger.warning(f"章节内容为空: 第 {i} 章 - {chapter_title}")

        except Exception as e:
            error_msg = f"第 {i} 章爬取出错: {e}"
            print(f"❌ {error_msg}")
            logger.error(error_msg)
            continue

        # 添加延迟，避免请求过快
        if i < total_chapters:
            delay = random.uniform(1.5, 2.5)
            print(f"⏳ 等待 {delay:.1f} 秒...")
            time.sleep(delay)

    end_time = datetime.now()
    duration = end_time - start_time

    print("\n" + "=" * 60)
    print(f"🎉 爬取完成！")
    print(f"📚 成功爬取: {len(all_chapters)}/{total_chapters} 章")
    print(f"⏱️ 用时: {duration}")
    print(f"💾 文件保存: {filename}")

    logger.info(f"小说爬取完成: {novel_title}, 成功 {len(all_chapters)}/{total_chapters} 章, 用时 {duration}")

    return all_chapters

def save_chapters_list_to_file(chapters_data, filename='章节列表.txt'):
    """
    保存章节列表到文件

    Args:
        chapters_data (dict): 章节数据
        filename (str): 文件名
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"小说标题: {chapters_data['novel_title']}\n")
            f.write(f"获取章节数: {chapters_data['total_chapters']}\n")
            f.write(f"预期章节数: {chapters_data.get('expected_chapters', '未知')}\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")

            f.write("章节列表:\n")
            for i, chapter in enumerate(chapters_data['chapters'], 1):
                f.write(f"{i:4d}. {chapter['title']}\n")
                f.write(f"      {chapter['url']}\n\n")

        print(f"📄 章节列表已保存到: {filename}")
        logger.info(f"章节列表保存成功: {filename}")

    except Exception as e:
        error_msg = f"保存章节列表失败: {e}"
        print(f"❌ {error_msg}")
        logger.error(error_msg)

def load_chapter_urls_from_file(filename='章节列表.txt'):
    """
    从文件加载章节URL列表

    Args:
        filename (str): 文件名

    Returns:
        tuple: (章节信息列表, 小说标题)
    """
    chapters = []
    novel_title = '未知小说'

    try:
        if not os.path.exists(filename):
            print(f"⚠️ 文件不存在: {filename}")
            return chapters, novel_title

        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        current_title = None

        for line in lines:
            line = line.strip()

            # 提取小说标题
            if line.startswith('小说标题:'):
                novel_title = line.replace('小说标题:', '').strip()
                continue

            if line and not line.startswith('获取章节数:') and not line.startswith('预期章节数:') and not line.startswith('生成时间:') and not line.startswith('=') and not line.startswith('章节列表:'):
                # 匹配章节行 (格式: "  1. 第1章 穿越")
                if re.match(r'^\s*\d+\.\s+', line):
                    # 提取章节标题
                    title_match = re.search(r'^\s*\d+\.\s+(.+)$', line)
                    if title_match:
                        current_title = title_match.group(1).strip()
                # 匹配URL行
                elif line.startswith('      https://') and current_title:
                    url = line.strip()
                    chapters.append({'title': current_title, 'url': url})
                    current_title = None
                elif line.startswith('https://') and current_title:
                    url = line.strip()
                    chapters.append({'title': current_title, 'url': url})
                    current_title = None

        print(f"📖 从文件加载章节列表: {len(chapters)} 章")
        logger.info(f"章节列表加载成功: {filename}, {len(chapters)} 章")

    except Exception as e:
        error_msg = f"读取文件失败: {e}"
        print(f"❌ {error_msg}")
        logger.error(error_msg)

    return chapters, novel_title

def display_menu():
    """显示主菜单"""
    print("\n" + "=" * 60)
    print("🚀 小说爬虫程序 v2.0")
    print("=" * 60)
    print("1. 📋 获取章节列表")
    print("2. 📚 爬取小说内容")
    print("3. 🔄 获取章节列表并爬取小说")
    print("4. ❌ 退出程序")
    print("=" * 60)

def get_user_choice():
    """获取用户选择"""
    while True:
        try:
            choice = input("请选择功能 (1-4): ").strip()
            if choice in ['1', '2', '3', '4']:
                return int(choice)
            else:
                print("⚠️ 请输入有效的选项 (1-4)")
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            return 4
        except Exception:
            print("⚠️ 输入错误，请重新输入")

def get_novel_url():
    """获取小说URL"""
    while True:
        url = input("请输入小说目录页URL: ").strip()
        if url:
            if not url.startswith('http'):
                print("⚠️ 请输入完整的URL（以http开头）")
                continue
            return url
        else:
            print("⚠️ URL不能为空")

def process_get_chapters_list():
    """处理获取章节列表"""
    print("\n📋 获取章节列表")
    print("-" * 40)

    url = get_novel_url()

    print(f"🔍 开始获取章节列表: {url}")
    chapters_data = get_all_chapters_list(url)

    if chapters_data and chapters_data['chapters']:
        # 保存章节列表
        save_chapters_list_to_file(chapters_data, '章节列表.txt')

        print(f"\n📊 获取结果:")
        print(f"   小说标题: {chapters_data['novel_title']}")
        print(f"   获取章节: {chapters_data['total_chapters']} 章")
        print(f"   预期章节: {chapters_data.get('expected_chapters', '未知')} 章")

        return True
    else:
        print("❌ 获取章节列表失败")
        return False

def process_crawl_novel():
    """处理爬取小说内容"""
    print("\n📚 爬取小说内容")
    print("-" * 40)

    # 尝试从文件加载章节列表
    chapters, novel_title = load_chapter_urls_from_file('章节列表.txt')

    if not chapters:
        print("⚠️ 未找到章节列表文件，请先获取章节列表")
        return False

    print(f"📖 准备爬取: 《{novel_title}》")
    print(f"📊 章节数量: {len(chapters)} 章")

    # 确认是否继续
    confirm = input("是否开始爬取？(y/n): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("❌ 用户取消操作")
        return False

    # 开始爬取
    chapters_content = crawl_all_chapters(chapters, novel_title)

    if chapters_content:
        print(f"\n🎉 爬取完成！成功爬取 {len(chapters_content)} 章")
        return True
    else:
        print("❌ 爬取失败")
        return False

def process_full_workflow():
    """处理完整工作流程"""
    print("\n🔄 获取章节列表并爬取小说")
    print("-" * 40)

    url = get_novel_url()

    # 第一步：获取章节列表
    print(f"\n📋 步骤1: 获取章节列表")
    chapters_data = get_all_chapters_list(url)

    if not chapters_data or not chapters_data['chapters']:
        print("❌ 获取章节列表失败，无法继续")
        return False

    # 保存章节列表
    save_chapters_list_to_file(chapters_data, '章节列表.txt')

    print(f"\n📊 章节列表获取成功:")
    print(f"   小说标题: {chapters_data['novel_title']}")
    print(f"   获取章节: {chapters_data['total_chapters']} 章")

    # 确认是否继续爬取
    confirm = input("\n是否继续爬取小说内容？(y/n): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("✅ 章节列表已保存，可稍后使用功能2进行爬取")
        return True

    # 第二步：爬取小说内容
    print(f"\n📚 步骤2: 爬取小说内容")
    chapters_content = crawl_all_chapters(chapters_data['chapters'], chapters_data['novel_title'])

    if chapters_content:
        print(f"\n🎉 完整流程完成！成功爬取 {len(chapters_content)} 章")
        return True
    else:
        print("❌ 爬取小说内容失败")
        return False

def main():
    """主函数"""
    try:
        print("🚀 小说爬虫程序启动")
        logger.info("程序启动")

        while True:
            display_menu()
            choice = get_user_choice()

            if choice == 1:
                # 获取章节列表
                success = process_get_chapters_list()
                if success:
                    print("✅ 章节列表获取完成")
                else:
                    print("❌ 章节列表获取失败")

            elif choice == 2:
                # 爬取小说内容
                success = process_crawl_novel()
                if success:
                    print("✅ 小说爬取完成")
                else:
                    print("❌ 小说爬取失败")

            elif choice == 3:
                # 完整工作流程
                success = process_full_workflow()
                if success:
                    print("✅ 完整流程执行完成")
                else:
                    print("❌ 流程执行失败")

            elif choice == 4:
                # 退出程序
                print("👋 感谢使用小说爬虫程序！")
                logger.info("程序正常退出")
                break

            # 询问是否继续
            if choice != 4:
                continue_choice = input("\n是否继续使用程序？(y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes', '是']:
                    print("👋 感谢使用小说爬虫程序！")
                    logger.info("程序正常退出")
                    break

    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
        logger.info("程序被用户中断")
    except Exception as e:
        error_msg = f"程序执行出错: {e}"
        print(f"❌ {error_msg}")
        logger.error(error_msg)
    finally:
        # 清理临时文件
        temp_files = ['小说爬虫日志.log']
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    # 保留日志文件，不删除
                    pass
                except:
                    pass

if __name__ == "__main__":
    main()